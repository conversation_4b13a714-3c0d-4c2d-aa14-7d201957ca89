/********************************/
/****   Grand Livre & PL   ******/
/********************************/

-- 1. Construction de la requête dynamique selon le jour ouvré
	--IF @is_jourOuvre = 1
	--BEGIN
	---- Si c'est un jour ouvré, on extrait uniquement les données du mois courant
	--SET @sql = '
	--	SELECT 
	--	A.RLDNR,
	--	A.RBUKRS,
	--	A.BELNR,
	--	A.RACCT,
	--	A.SCNTR,
	--	A.PRCTR,
	--	A.PPRCTR, 
	--	A.HSL,
	--	A.RHCUR,
	--	A.MSL, 
	--	A.RUNIT, 
	--	A.FISCYEARPER, 
	--	A.LSTAR, 
	--	A.ACCAS, 
	--	A.OBJNR,  
	--	A.SGTXT, 
	--	A.BLART, 
	--	A.RFAREA, 
	--	S.SETNA<PERSON>, 
	--	K.MCOD1
	--	INTO ##GLPL
	--	FROM OPENQUERY(SAP_P100_PROD, 
	--		''SELECT
	--	A.RLDNR,
	--	A.RBUKRS,
	--	A.BELNR,
	--	A.RACCT,
	--	A.SCNTR,
	--	A.PRCTR,
	--	A.PPRCTR, 
	--	A.HSL,
	--	A.RHCUR,
	--	A.MSL, 
	--	A.RUNIT, 
	--	A.FISCYEARPER,
	--	A.BUDAT,
	--	A.LSTAR, 
	--	A.ACCAS, 
	--	A.OBJNR,  
	--	A.SGTXT, 
	--	A.BLART, 
	--	A.RFAREA, 
	--	S.SETNAME, 
	--	K.MCOD1
	--FROM SAPHANADB.ACDOCA A
	--LEFT JOIN SAPHANADB.SETLEAF S ON A.RACCT = S.SETNAME 
	--	AND S.SETCLASS = 0102 
	--	AND S.VALFROM = ''00''
	--LEFT JOIN SAPHANADB.SKAT K ON K.MCOD1 = CONCAT(''00'', A.RACCT)
	--	AND K.SPRAS = ''0102'' 
	--	AND K.KTOPL = ''ZGFI''
	--		WHERE LEFT(BUDAT, 6) = ''''' + REPLACE(@periode, '-', '') + ''''''')';
	--END
	--ELSE
	--BEGIN
	---- Sinon, on extrait toutes les données de l'année courante
	--SET @sql = '
	--	SELECT 
	--	A.RLDNR,
	--	A.RBUKRS,
	--	A.BELNR,
	--	A.RACCT,
	--	A.SCNTR,
	--	A.PRCTR,
	--	A.PPRCTR, 
	--	A.HSL,
	--	A.RHCUR,
	--	A.MSL, 
	--	A.RUNIT, 
	--	A.FISCYEARPER,
	--	A.BUDAT,
	--	A.LSTAR, 
	--	A.ACCAS, 
	--	A.OBJNR,  
	--	A.SGTXT, 
	--	A.BLART, 
	--	A.RFAREA, 
	--	S.SETNAME, 
	--	K.MCOD1
	--	INTO ##GLPL
	--	FROM OPENQUERY(SAP_P100_PROD, 
	--		''SELECT 
	--	A.RLDNR,
	--	A.RBUKRS,
	--	A.BELNR,
	--	A.RACCT,
	--	A.SCNTR,
	--	A.PRCTR,
	--	A.PPRCTR, 
	--	A.HSL,
	--	A.RHCUR,
	--	A.MSL, 
	--	A.RUNIT, 
	--	A.FISCYEARPER, 
	--	A.LSTAR, 
	--	A.ACCAS, 
	--	A.OBJNR,  
	--	A.SGTXT, 
	--	A.BLART, 
	--	A.RFAREA, 
	--	S.SETNAME, 
	--	K.MCOD1
	--FROM SAPHANADB.ACDOCA A
	--LEFT JOIN SAPHANADB.SETLEAF S ON A.RACCT = S.SETNAME 
	--	AND S.SETCLASS = 0102 
	--	AND S.VALFROM = ''00''
	--LEFT JOIN SAPHANADB.SKAT K ON K.MCOD1 = CONCAT(''00'', A.RACCT)
	--	AND K.SPRAS = ''0102'' 
	--	AND K.KTOPL = ''ZGFI''
	--			WHERE LEFT(BUDAT, 4) = ''''' + @annee + ''''''')';
	--END

	---- 2. Exécution de la requête dynamique pour créer et remplir la table temporaire ##GLPL
	--EXEC(@sql);

	---- 3. Mise à jour de la colonne BUDAT pour remplacer la date SAP par l'identifiant de la dimension temps
	--UPDATE A
	--SET BUDAT = DT.idtemps
	--FROM ##GLPL A
	--INNER JOIN dimtemps DT
	--ON DT.jour = CAST(LEFT(A.BUDAT, 4) + '-' + SUBSTRING(A.BUDAT, 5, 2) + '-' + RIGHT(A.BUDAT, 2) AS DATE);

	---- 4. Si la table temporaire contient des données, on continue le traitement
	--IF EXISTS (SELECT * FROM ##GLPL)
	--BEGIN
	---- 4a. Suppression des anciennes données dans la table cible GLPL pour la période concernée
	--IF @is_jourOuvre = 1
	--BEGIN
	--	-- Si jour ouvré, on supprime uniquement les données du mois courant
	--	DELETE GLPL
	--	WHERE BUDAT IN (
	--		SELECT idtemps FROM dimtemps WHERE YEAR(jour) = @annee AND MONTH(jour) = @mois
	--	);
	--END
	--ELSE
	--BEGIN
	--	-- Sinon, on supprime toutes les données de l'année courante
	--	DELETE GLPL
	--	WHERE BUDAT IN (
	--		SELECT idtemps FROM dimtemps WHERE YEAR(jour) = @annee
	--	);
	--END

	---- 4b. Insertion des nouvelles données dans la table cible GLPL
	--INSERT INTO GLPL (RACCT, SGTXT, BLART, RFAREA, BUDAT)
	--SELECT RACCT, SGTXT, BLART, RFAREA, BUDAT
	--FROM ##GLPL;

	---- 4c. Suppression de la table temporaire
	--DROP TABLE ##GLPL;

	---- 4d. Enregistrement de l'opération dans la table de suivi
	--INSERT INTO suivi_chargements VALUES ('Grand_Livres', GETDATE(), 'GLPL');
	--END
