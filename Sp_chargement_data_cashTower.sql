USE [RAPPORT_DI_DEV]
GO

/****** Object:  StoredProcedure [dbo].[sp_chargement_data_CashTower]    Script Date: 05/02/2025 13:55:51 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:	B.VINZERICH
-- Create date: 11/09/2025
-- Description:	Chargement des données pour le 
--              PowerBI "CashTower"   
-- =============================================

ALTER PROCEDURE [dbo].[sp_chargement_data_CashTower] (@annee int)
AS
BEGIN
-- declare @annee int = 2024	



-- Variables
set @annee  = isnull(@annee,year(dateadd(d,-10,getdate())))
declare @mois int = month(dateadd(d,-10,getdate()))
declare @periode date = rtrim(@annee) + '-' + case when @mois < 10 then '0' + rtrim(@mois) else rtrim(@mois) end  + '-01'
declare @periode2 date = dateadd(m,-1,@periode)
declare @sql nvarchar(3000)
declare @liste_soc nvarchar(500)
       
-- C'est le dernier chargement du fichier celonis qui donne la période de cloture :
declare @derniere_cloture char(6)
select @derniere_cloture = max(left(CONVERT(VARCHAR, date_chargement, 112),6)) from suivi_chargements where [file] = 'Extraction_Celonis.xlsx'


-- pour l'encaissement
declare @PathDirectory nvarchar(100)
declare @PathDirectoryBackUp nvarchar(100) 
declare @fullpath nvarchar(1000)
declare @cursor_file nvarchar(1000)
declare @Cmd nvarchar(3000)


-- Pour les envois de mail
declare @table_notification AS TableauDonnees
declare @destinataires nvarchar(300) = '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
declare @objet nvarchar(150) = 'sp_chargement_data_Rapport_DI.'
declare @texte as varchar(MAX) =''




-- Suppression des tables temporaires
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##VBRK') drop table ##VBRK
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Target') drop table ##Target
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Celonis') drop table ##Celonis
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Euler') drop table ##Euler
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##encaissement') drop table ##encaissement


-- Initialisation de tables temporaires
IF OBJECT_ID('tempdb..#DirTree') IS NOT NULL DROP TABLE #DirTree
CREATE TABLE #DirTree (Id int identity(1,1),
						SubDirectory nvarchar(255),
						Depth int,
						FileFlag int)

IF OBJECT_ID('tempdb..#DirTreeBackup') IS NOT NULL DROP TABLE #DirTreeBackup
CREATE TABLE #DirTreeBackup (Id int identity(1,1),
						SubDirectory nvarchar(255),
						Depth smallint,
						FileFlag bit)


-- Tables des anomalies
IF OBJECT_ID('tempdb..#anomalies') IS NOT NULL DROP TABLE #anomalies
CREATE TABLE #anomalies(nature nvarchar(150),gravite nvarchar(20)) ON [PRIMARY]


/********************************/
/****      CT_REFDAF       ******/
/********************************/

if exists (select distinct profitcode,L2,bl_id 
from PA_Profit_Center pa 
inner join CT_liste_BL bl on bl.bl_name_tm1 = GBL
where isnumeric(profitcode) = 1 and annee = @annee)
begin 
	truncate table CT_refdaf
	insert into CT_refdaf
	select distinct profitcode,L2,bl_id 
	from PA_Profit_Center pa 
	inner join CT_liste_BL bl on bl.bl_name_tm1 = GBL
	where isnumeric(profitcode) = 1 and annee = @annee
end


/********************************/
/****       FACTURES       ******/
/********************************/

select @liste_soc = '('+STRING_AGG(company_code,',') + ')' FROM ct_liste_societes where is_active = 1


-- On récupère le mois en cours et le mois précédent - NOUVELLE VERSION
set @sql = 'select * INTO ##VBRK from openquery(SAP_P100_PROD,''SELECT E.VBELN,FKDAT,E.BUKRS,E.KUNRG,P.PRCTR,SUM(P.SKFBP) * case p.SHKZG when ''''X'''' then -1 else 1 end MONTANT
FROM SAPHANADB.VBRK E 
INNER JOIN SAPHANADB.VBRP P ON E.VBELN = P.VBELN
WHERE E.MANDT = 100 
AND E.DRAFT <> ''''X''''
AND LEFT(E.KUNRG,3) = ''''000'''' -- retire les factures interco
AND VBTYP <> ''''U'''' 
AND E.BUKRS in ' + @liste_soc + ' 
AND (FKDAT like ''''' + 
rtrim(year(@periode2)) + case when month(@periode2) < 10 then '0' else '' end + rtrim(month(@periode2)) + '%'''' OR FKDAT like ''''' + 
rtrim(year(@periode)) + case when month(@periode) < 10 then '0' else '' end + rtrim(month(@periode)) + '%'''') 
GROUP BY E.VBELN,FKDAT,E.BUKRS,E.KUNRG,P.PRCTR,P.SHKZG'')'
exec(@sql)


update ##VBRK set PRCTR = substring(PRCTR,5,6) where left(PRCTR,4) = '0000'

-- Répartion par BL
if not exists (select * from ##VBRK where isnumeric(montant) = 0)
begin	
	delete CT_facture where annee = year(@periode) and mois = month(@periode)
	delete CT_facture where annee = year(@periode2) and mois = month(@periode2)
	insert into CT_facture
	select left(v.fkdat,4) as annee, substring(fkdat,5,2) as mois,v.VBELN as no_facture,bukrs as company_code,kunrg,prctr as cost,cast(montant as float) as montant,
	isnull(bl.bl_id,1) bl_id 
	from ##VBRK v
	left outer join REPOSITORY.dbo.REFDAF_TM1 r on r.annee = left(v.fkdat,4) and RTRIM(v.prctr) = r.cost
	left outer join CT_liste_BL bl on r.gbl = bl.bl_name_tm1

	
	INSERT INTO suivi_chargements values('VBRK',GETDATE(),'CT_Facturation')
end
else
	insert  #anomalies values('Les factures ne sont pas récupérées suite à une valeur non numérique.','BLOQUANT')



/********************************/
/****       TARGET         ******/
/********************************/


set @PathDirectory = 'G:\RAPPORT_DI\CASH_TOWER\Target\A_traiter\'
set @PathDirectoryBackUp = 'G:\RAPPORT_DI\CASH_TOWER\Target\Archives\'


-- On récupères le contenu du dossier à traiter
truncate table #Dirtree
set @sql ='INSERT INTO #DirTree (SubDirectory, Depth, FileFlag) 
EXEC master..xp_dirtree ''' + @PathDirectory +''', 10, 1'
exec(@sql)

delete #DirTree where Depth<>1 or FileFlag<>1  
delete #DirTree where SubDirectory not like '%target%'

select top 1 @cursor_file = SubDirectory from #DirTree

set @fullpath = @PathDirectory + @cursor_file

if @fullpath is not null
begin try

	set @sql = 
	'select *
	into ##target
	FROM OPENDATASOURCE
	(''Microsoft.ACE.OLEDB.12.0'',''Data Source="'+ @fullpath+ '";Extended Properties="Excel 12.0 Xml;HDR=NO"'')...[BDD_Base_target$]'
	EXEC sp_executesql @SQL

	delete ##target where isnumeric(f4) = 0 or cast(f4 as float) = 0

	if exists (select * from  ##target)
		delete CT_Target where annee in (select distinct YEAR(f6) from ##target)

	insert into CT_Target
	select year(F6),case  
	when left(F7,2) = 'Ja' then 1  
	when left(F7,1) = 'F' then 2  
	when left(F7,3) = 'Mar' then 3  
	when left(F7,2) = 'Av' then 4  
	when left(F7,1) = 'M' then 5  
	when left(F7,4) = 'Juin' then 6  
	when left(F7,1) = 'J' then 7  
	when left(F7,1) = 'A' then 8  
	when left(F7,1) = 'S' then 9  
	when left(F7,1) = 'O' then 10 
	when left(F7,1) = 'N' then 11 
	else 12 end,(cast(F4 as float)) as montant,isnull(bl.bl_id,1) bl_id
	from ##target t
	left outer join CT_liste_bl bl on bl.bl_name_target = t.f2


		--Le fichier est archivé
	set @Cmd = 'MOVE /Y "'+ @fullpath + '" '  + @PathDirectoryBackUp
	EXEC master..xp_CMDShell @Cmd

end try
begin catch
	insert #anomalies values('Erreur de chargement du fichier "Target_' + rtrim(@annee) + '.csv".','NON BLOQUANT')
end catch


-- CT_target_Encaissement : Chargé manuellement depuis la fichier "Pilotage - Cash Tower - V12.xlsx"



/**********************************************/
/***         BALANCE AGEE (evolution)      ****/
/**********************************************/



if exists (select * from view_CT_Balance_agee)
begin
	delete CT_balance_evol where periode = (select FORMAT(GETDATE(), 'MM/yyyy'))
	insert into CT_balance_evol
	select max(FORMAT(GETDATE(), 'MM/yyyy')) periode,round(sum(montant_echu + montant_a_echoir),0) as 'Total_AR',round(sum(montant_echu),0) 'Past_due',
	round(sum([1 - 30 jours]+[31 - 60 jours]),0) as '< 60' 
	from view_CT_Balance_agee
end


/**********************************************/
/***         BALANCE AGEE FIN DE MOIS      ****/
/**********************************************/

-- à J+3 on historise la balance agée
declare @date_mois_prec date = dateadd(d,-1,DATEADD(DAY, 1, EOMONTH(getdate(), -1)))

if (select day(getdate())) in (3,4,5) and not exists (select * from Balance_agee_finmois where date_valeur = @date_mois_prec)
begin
	delete Balance_agee_finmois where date_valeur = @date_mois_prec
	insert into Balance_agee_finmois select *,@date_mois_prec from Balance_agee
end


/*****************************/ 
/*****    ENCAISSEMENT  ******/  
/*****************************/


/****  METHODE PAR LECTURE DIRECTE DANS SAP ******/

if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##encaissementK') drop table ##encaissement

set @sql = 'select  * into ##encaissement from openquery(SAP_P100_PROD,''SELECT 
RBUKRS,GKONT,BUDAT,SUM(HSL) MONTANT,PRCTR
FROM SAPHANADB.ACDOCA
WHERE RCLNT = 100 AND RLDNR = ''''0L'''' 
and BLART =''''DZ''''
AND GJAHR = ' + rtrim(@annee) + '
AND RACCT like ''''0051%''''
GROUP BY RBUKRS,GKONT,BUDAT,PRCTR'')'
exec(@sql)

insert into #anomalies 
SELECT 'Cet encaissement de ' + rtrim(montant) + ' pour client ' + RTRIM(GKONT) + ' est rattaché au cost ' +  PRCTR,'NON BLOQUANT'
from ##encaissement where isnumeric(prctr) = 0 

-- Le cost DUMMY est remplacé par le cost "GAP France"
update ##encaissement set prctr  = '100590' WHERE  isnumeric(prctr) = 0  


-- Suppression des encaissements GROUPE
delete e from ##encaissement e
inner join repository.dbo.SAP_CUSTOMER c on n_sap = GKONT
where secteur = 'grp' or Canal_Dist = '90'


-- Suppression des sociétés hors scope
delete ##encaissement where rbukrs not in (select company_code from ct_liste_societes where is_active = 1)


-- Mise à jour de la table pour l'année en cours
if exists (select * from ##encaissement)
begin
	delete CT_encaissement where annee = @annee

	insert into CT_encaissement
	select YEAR(BUDAT) ANNEE,MONTH(BUDAT) MOIS,DAY(BUDAT) JOUR,RBUKRS COMPANY_CODE,cast(SUM(MONTANT) / 1000 as decimal(10,2)) MONTANT,CAST(PRCTR AS BIGINT)
	from ##encaissement group by RBUKRS,BUDAT,PRCTR
end




/**** ANCIENNE METHODE DE CHARGEMENT PAR FICHIERS EXCEL QUI N'EST PLUS UTILISEE ******

set @PathDirectory = 'G:\RAPPORT_DI\CASH_TOWER\Encaissements\A_traiter\'
set @PathDirectoryBackUp = 'G:\RAPPORT_DI\CASH_TOWER\Encaissements\Archives\'

-- On récupères le contenu du dossier à traiter
truncate table #Dirtree
set @sql ='INSERT INTO #DirTree (SubDirectory, Depth, FileFlag) 
EXEC master..xp_dirtree ''' + @PathDirectory +''', 10, 1'
exec(@sql)

delete #DirTree where Depth<>1 or FileFlag<>1  
delete #DirTree where SubDirectory not like '%Encaissements%.xlsx'


-- supprime les fichiers qui ont déjà été traités
INSERT INTO #DirTreeBackup (SubDirectory, Depth, FileFlag) EXEC master..xp_dirtree @PathDirectoryBackUp, 10, 1

delete #DirTree where SubDirectory in (select SubDirectory from #DirTreeBackup)


-- Import des fichiers encaissements
IF OBJECT_ID('tempdb..#Template_encaissements') IS NOT NULL DROP TABLE #Template_encaissements
CREATE TABLE #Template_encaissements (soc char(3),date_valeur date,devise nvarchar(150),montant float) ON [PRIMARY]
IF OBJECT_ID('tempdb..#import_brut') IS NOT NULL DROP TABLE  #import_brut
CREATE TABLE #import_brut (F1 nvarchar(300),F2 nvarchar(300),F3 nvarchar(300),F4 nvarchar(300)) ON [PRIMARY]

-- On boucle sur les fichiers
declare c_file cursor for	
	select SubDirectory from #DirTree where SubDirectory like '%encaissements%.xlsx' 
	
	open c_file 
	fetch next from c_file into @cursor_file
	while @@fetch_status = 0
	begin

		set @fullpath = @PathDirectory +  @cursor_file

		-- Controle au préalable les noms des colonnes et les positions.
		truncate table #Template_encaissements 	
		truncate table #import_brut	
		
		begin try

			set @sql = '
			INSERT INTO #import_brut
			select [F1],[F6],[F7],[F8]
			FROM OPENDATASOURCE
			(''Microsoft.ACE.OLEDB.12.0'',''Data Source="' + @fullpath + '";Extended Properties="Excel 12.0 Xml;HDR=NO"'')...[Sheet0$]'
			exec(@sql)

			insert into #Template_encaissements 		
			select left([F1],3),CONVERT(date,[F2],103),[F3],replace(replace([F4],' ',''),',','.') FROM #import_brut
			where  TRY_CONVERT(date, [F2],103) IS NOT NULL


			-- Si le fichier est vide c'est que la colonne de la date de valeur n'est pas à sa place.
			if not exists(select * from #Template_encaissements)
			begin
				insert into #anomalies values('Le fichier ' + @cursor_file + ' ne respecte pas le format. La date de valeur n''est pas à sa place. Il ne sera pas importé.','non bloquant')
				goto fichier_suivant
			end

						
			-- On supprime les sociétés SW
			delete #Template_encaissements  where '1'+ rtrim(SOC) not in (select company_code from CT_liste_societes where is_active = 1)

			-- On convertit en k€
			update #Template_encaissements set montant = round(montant / 1000,1)

			delete CT_encaissement_treso where CONVERT(VARCHAR(10), DATEFROMPARTS(annee, mois, jour), 23) in (select distinct date_valeur from #Template_encaissements) 

			insert into CT_encaissement_treso 
			select year(date_valeur),month(date_valeur),day(date_valeur),'1' + rtrim(soc),sum(montant) from #Template_encaissements
			group by soc,date_valeur 

			--Le fichier est archivé
			set @Cmd = 'MOVE /Y "'+ @fullpath + '" '  + @PathDirectoryBackUp
			EXEC master..xp_CMDShell @Cmd

			fichier_suivant:

		end try
		begin catch
			insert into #anomalies values('Le fichier ' + @cursor_file + ' ne respecte pas le format. Il ne sera pas importé.','non bloquant')
		end catch
  
		fetch next from c_file into @cursor_file

	end

close c_file
deallocate c_file

-- FIN DU BLOC "ANCIENNE METHODE DE CHARGEMENT PAR FICHIERS EXCEL QUI N'EST PLUS UTILISEE" */



/************************************/ 
/*****   Encours FAE/PCA       ******/ 
/*****  fichier Excel CELONIS  ******/
/************************************/



set @PathDirectory = 'G:\RAPPORT_DI\CASH_TOWER\Encours\A_traiter\'
set @PathDirectoryBackUp = 'G:\RAPPORT_DI\CASH_TOWER\Encours\Archives\'


-- On récupères le contenu du dossier à traiter
truncate table #DirTree 
set @sql ='INSERT INTO #DirTree (SubDirectory, Depth, FileFlag) EXEC master..xp_dirtree ''' + @PathDirectory +''', 10, 1'
exec(@sql)

delete #DirTree where Depth<>1 or FileFlag<>1 

select top 1 @cursor_file =SubDirectory from #DirTree
set @fullpath = @PathDirectory + @cursor_file


if exists(select * from #DirTree)

begin
	set @sql = 'select * into ##CELONIS
	FROM OPENDATASOURCE
	(''Microsoft.ACE.OLEDB.12.0'',''Data Source="' + @fullpath + '";Extended Properties="Excel 12.0 Xml;HDR=NO"'')...[Sheet_0$]'
	exec(@sql)

	update ##CELONIS set F6 = '0' where F6 is null
	update ##CELONIS set F5 = '0' where F5 is null
	update ##CELONIS set F6 =  replace(F6,',','.'),F5 = replace(replace(F5,' ',''),',','.')


-- regroupement par eotp
	select F1,F2,F3,cast(replace(F5,',','.') as float) PCA,cast(replace(replace(F6,' ',''),',','.') as float) FAE,MAX(F4) COST
	into #CELONIS_EOTP
	FROM ##CELONIS c 
	group by f1,F2,F3,F5,F6

	delete #CELONIS_EOTP where abs(PCA) < 0.1 and abs(FAE) < 0.1

	if exists (select * from #CELONIS_EOTP)
	begin
		
		delete CT_encours where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2)
		
		insert into CT_encours
		select company_code,LEFT(F1,7) no_client,cast(p.profitcenter as BIGINT) profitcenter,sum(FAE) / 1000,sum(PCA) / 1000,left(@derniere_cloture,4),right(@derniere_cloture,2)
		from #CELONIS_EOTP c
		left outer join (select [Définition de projet],max(division) company_code,max([centre de profit]) as profitCenter from projets_SAP 
				     group by [Définition de projet]) p on p.[Définition de projet] = c.F2
		group by company_code,LEFT(F1,7),p.profitcenter
		having abs(sum(FAE)) >= 0.1 or abs(sum(PCA)) >= 0.1
	
	end 

	--Le fichier est archivé
	set @cmd = 'REN "' + @fullpath + '" ' + replace(@cursor_file,'.xlsx','') + '_' + @derniere_cloture + '.xlsx'
	EXEC master..xp_CMDShell @Cmd
	set @fullpath = @PathDirectory +  replace(@cursor_file,'.xlsx','') + '_' + @derniere_cloture + '.xlsx'
	set @Cmd = 'MOVE /Y "'+ @fullpath + '" '  + @PathDirectoryBackUp
	EXEC master..xp_CMDShell @Cmd
	

end -- eof : if exists(select * from #DirTree)




/*********************************/
/*****  CA des 3 derniers mois   */
/*********************************/

-- La table CT_CA_3mois va recevoir le total CA des 3 derniers mois par client
-- Le mois M de cette table aura donc la somme des CA des mois M, M-1 et M-2

declare @debut_periode char(6)

SET @debut_periode = replace(SUBSTRING(CONVERT(CHAR(8),dateadd(m,-2,CONVERT(DATE, LEFT(@derniere_cloture, 4) + '-' + RIGHT(@derniere_cloture, 2) + '-01')), 112), 1, 7),'-','')

SELECT left(@derniere_cloture,4) annee,right(@derniere_cloture,2) mois,[Company Code],customer_code,sum(cast(replace(valeur,',','.') as float) * 1.2 ) as 'CA',isnull(rd.bl_id,1) bl_id -- 1,2 = TVA
into #CT_CA_3mois
FROM SAP_PA_MARGIN pa 
left outer join ref_Customer rc on rc.Id_Customer = pa.Customer_id
left outer join CT_refdaf rd on rd.Profitcenter = pa.[PROFIT CENTER]
where rtrim(pa.annee)+case when pa.mois <10 then '0'+ rtrim(pa.mois) else rtrim(pa.mois) end between @debut_periode and @derniere_cloture
and pa.[Company Code] in (select company_code from CT_liste_societes where is_active = 1)
and isnumeric(rc.Customer_code) =1 
and Indicateurs like 'SALES01%'
group by [Company Code],customer_code,rd.bl_id
having sum(cast(replace(valeur,',','.') as float)) > 0

if exists (select * from  #CT_CA_3mois)
begin
	delete CT_CA_3mois where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2)
	insert into CT_CA_3mois select * from #CT_CA_3mois
end 


/*********************************/ 
/*****   TCC historisation  ******/  
/*********************************/


-- Calcul de TCC_HISTO

if not exists(select * from CT_TCC_histo where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2)) and exists (select * from view_CT_CA_M3  where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2))
insert into CT_TCC_histo
select left(@derniere_cloture,4),right(@derniere_cloture,2),a.[customer_code],a.bl_id,
round(cast(encours as float) + isnull(FAE,0) + isnull(pca,0),0) GCO_M1,
round(case sum(ca) when 0 then 0 else  (SUM(ca) * 1000 / 90) end,0) CA_JRS_M1
from view_CT_CA_M3 a
left outer join (select  no_client,bl_id,sum(Encours) encours
				from  view_CT_Encours_TCC v
				inner join ct_refdaf r on r.Profitcenter = v.ProfitCenter
				group by no_client,bl_id) b on b.no_client = a.[customer_code] and a.bl_id = b.bl_id
LEFT OUTER JOIN (select customer_code,bl_id,cast(sum(fae) * 1000 as BIGINT) AS FAE,CAST(sum(pca) * 1000 as BIGINT) AS pca from view_CT_Encours_FAE_PCA
				where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2) group by customer_code,bl_id) d on d.customer_code = a.customer_code and a.bl_id = d.bl_id
where annee = left(@derniere_cloture,4) and mois = right(@derniere_cloture,2)
group by a.[customer_code],a.bl_id,encours,fae,pca
order by sum(ca) desc



/************************************/ 
/*****    Notation CLIENTS     ******/ 
/*****   fichier Excel EULER   ******/
/************************************/

set @PathDirectory = 'G:\RAPPORT_DI\CASH_TOWER\Euler\A_traiter\'
set @PathDirectoryBackUp = 'G:\RAPPORT_DI\CASH_TOWER\Euler\Archives\'

-- On récupères le contenu du dossier à traiter
truncate table #DirTree 
set @sql ='INSERT INTO #DirTree (SubDirectory, Depth, FileFlag) EXEC master..xp_dirtree ''' + @PathDirectory +''', 10, 1'
exec(@sql)

delete #DirTree where Depth<>1 or FileFlag<>1 

select top 1 @cursor_file =SubDirectory from #DirTree
set @fullpath = @PathDirectory + @cursor_file

if (select COUNT(*) from #DirTree) = 1 

begin
	set @sql = 'select * into ##EULER
	FROM OPENDATASOURCE
	(''Microsoft.ACE.OLEDB.12.0'',''Data Source="' + @fullpath + '";Extended Properties="Excel 12.0 Xml;HDR=NO"'')...[Sheet1$]'
	exec(@sql) 

	-- Supprime la ligne des titres
	delete ##euler where f1 is null


	-- Le fichier ne sera pas traité si la colonne F15 ne contient pas le montant garanti
	IF exists (select * from ##euler where isnumeric(f15) = 0)
	begin
		insert into #anomalies values('Le fichier EULER nommé ' + @cursor_file + ' ne respecte pas le format. Il ne sera pas importé.','non bloquant')
		goto fin_euler
	end
	

	
	select cast(n_sap as BIGINT) n_sap,e.f10,e.f15
	into #ref_customer_notation from repository.dbo.sap_customer c
	inner join #euler e on c.taxnum2 = e.f44 and F43 = 'SIREN'

	insert into #ref_customer_notation
	select n_sap,e.f10,e.f15 
	from repository.dbo.sap_customer c
	inner join #euler e on c.taxnum0 = e.f44 and F43 <> 'SIREN'
	where cast(n_sap as int) not in (select n_sap from #ref_customer_notation)


	-- Ajout des nouveaux clients
	insert into ref_Customer_notation
	select 	e.* from #ref_customer_notation e
	left outer join ref_Customer_notation rc on rc.customer_code = n_sap
	where rc.customer_code is null

	-- Mise à jour des notes
	update rc set customer_note = F10,customer_garantie = f15 
	from #ref_customer_notation e
	inner join ref_Customer_notation rc on rc.customer_code = n_sap


	--Le fichier est archivé
	set @cmd = 'REN "' + @fullpath + '" ' + replace(@cursor_file,'.xlsx','') + '_' + @derniere_cloture + '.xlsx'
	EXEC master..xp_CMDShell @Cmd
	set @fullpath = @PathDirectory +  replace(@cursor_file,'.xlsx','') + '_' + @derniere_cloture + '.xlsx'
	set @Cmd = 'MOVE /Y "'+ @fullpath + '" '  + @PathDirectoryBackUp
	EXEC master..xp_CMDShell @Cmd
	

end -- eof : (select COUNT(*) from #DirTree) = 1 

fin_euler:



-- Suppression des tables temporaires
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##VBRK') drop table ##VBRK
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Target') drop table ##Target
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Celonis') drop table ##Celonis
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##Euler') drop table ##Euler
if exists(select * FROM tempdb.sys.objects WHERE type = 'U' and name = '##encaissement') drop table ##encaissement

END
GO



