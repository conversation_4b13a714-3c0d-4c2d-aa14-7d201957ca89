SELECT [BELNR]   ---numéro de document
      ,[FISCYEARPER] -- période/année
      ,[BUDAT]
      ,[BLDAT]
      ,[HSL] -- montant
      ,[OBJNR] -- l’élément d’imputation (CC/WBS)
      ,[PERNR]
      ,[RUNIT] -- unité de la quantité
      ,[RVUNIT]
      ,[VMSL]
      ,[ULSTAR]
      ,[ISDD]
      ,[RFAREA]
  FROM [RAPPORT_DI].[dbo].[ACDOCA]
-- BELNR
-- FISCYEARPER
-- HSL
-- OBJNR
-- RUNIT
  ---------- Jointures à trouver

-- ACCAS : activité/réseau
-- LSTAR : type d’activité
-- MSL : quantité
-- RHCUR : devise
-- PPRCTR : centre de profit émetteur (du collaborateur)
-- PRCTR : centre de profit récepteur (du projet)
-- SCNTR : centre de coût / collaborateur
-- RACCT : nature comptable
-- RBUKRS : Société
-- RLDNR : Ledger

-- Tables ayant certaines colonnes
SELECT [PROJN]
      ,[OBJNR]
      ,[LARNT]
FROM [RAPPORT_DI].[dbo].[AFVC]

--
SELECT [VBELN]
      ,[KUNRG]
      ,[MONTANT]
      ,[FKDAT]
      ,[BUKRS] -- Société
      ,[PRCTR] -- centre de profit récepteur (du projet)
      ,[NETDT]
      ,[ECART_JOURS]
  FROM [RAPPORT_DI].[dbo].[Balance_agee]

--
SELECT TOP (1000) [ID_EOTP]
      ,[CODE_EOTP]
      ,[NOM_EOTP]
      ,[ID_PROJ]
      ,[PRCTR]
      ,[NO_CLIENT]
  FROM [RAPPORT_DI].[dbo].[ref_eotp]

  --

--   select * from openquery(SAP_P100_PROD,'SELECT *
-- FROM SAPHANADB.ACDOCA')

--select * from openquery (SAP_P100_PROD,'select * from SAPHANADB.ACDOCA')

--
USE RAPPORT_DI
GO

select * from openquery (SAP_P100_PROD,'select RLDNR,RBUKRS,BELNR,RACCT,SCNTR,PRCTR,PPRCTR,RHCUR,MSL,RUNIT,FISCYEARPER,ACCAS,OBJNR from SAPHANADB.ACDOCA')


select * from openquery (SAP_P100_PROD,'select RLDNR,RBUKRS,BELNR,RACCT,SCNTR,PRCTR,PPRCTR,RHCUR,MSL,RUNIT,FISCYEARPER,ACCAS,OBJNR from SAPHANADB.ACDOCA')


select * from openquery (SAP_P100_PROD,'select * from SAPHANADB.ACDOCA')


-----/ TEST /------

USE RAPPORT_DI_DEV
GO

SELECT TOP 10 * FROM [REPOSITORY].[dbo].[refdaf_tm1] WHERE annee = 2025

---------
USE RAPPORT_DI
GO

select * from openquery (SAP_P100_PROD,'select RLDNR,RBUKRS,BELNR,RACCT,SCNTR,PRCTR,PPRCTR,RHCUR,MSL,RUNIT,FISCYEARPER,ACCAS,OBJNR from SAPHANADB.ACDOCA')


select * from openquery (SAP_P100_PROD,'select RACCT, BLART, SGTXT, RFAREA from SAPHANADB.ACDOCA')

---
-- Info nœud hiérarchique : INFO NOEUD HIERARCHIQUE => select SETNAME from SETLEAF where SETCLASS=0102 and SETLEAF_VALFROM=”00” + ACDOCA_RACCT
-- DésLgue cpte général : select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1=ACDOCA_RACCT
-- Type écriture jrnal : ACDOCA_BLART
-- Nom type écrit.jrnal : Créer une table fictive dans la base de données contenant les données indiquées dans le fichier ci-joint
-- Txte poste écr.jrnal : ACDOCA_SGTXT
-- Domaine fonctionnel : ACDOCA_RFAREA

----- Création d'une table fictive contenant Nom type écrit.jrnal : Créer une table fictive dans la base de données contenant les données indiquées dans le fichier ci-joint

-- Création de la table fictives
CREATE TABLE [dbo].[Info_ACDOCA] (
    [CodeTypeDoc] VARCHAR(2) NOT NULL PRIMARY KEY,
    [Description] NVARCHAR(255) NULL
);

-- Insertion des données
INSERT INTO [dbo].[Info_ACDOCA] ([CodeTypeDoc], [Description])
VALUES
('13', 'Prov. Holidays'),
('14', 'Cancel Prov. Hol'),
('4R', 'ANUL. RECTICATIVA R4'),
('54', ''),
('58', ''),
('59', ''),
('AA', 'Écriture d''immo.'),
('AB', 'Ecritures RFA'),
('AC', 'Provisions CO'),
('AD', 'Provisions man. Clt'),
('AE', ''),
('AF', 'Comptab. amortiss.'),
('AG', 'SyGS, ServtSS, TUP'),
('AK', 'Provisions man. Frs'),
('AL', 'SyGS, ServtSS, TUP'),
('AN', 'Non utilisé'),
('AP', 'Imp. Immo. Period.'),
('AR', 'ARRENDA. LOCAL NEGOC'),
('AS', 'Provisions man. Gnl'),
('BE', 'SyGS, ServtSS, TUP'),
('CB', 'SyGS, ServtSS, TUP'),
('CC', 'Cts sec. inter-soc.'),
('CG', 'CCA'),
('CH', 'Non utilisé'),
('CI', 'SyGS, ServtSS, TUP'),
('CL', 'Écrit.exercice CL/OP'),
('CO', 'Reclass. analytique'),
('CQ', 'SyGS, ServtSS, TUP'),
('DA', 'Acompte clients'),
('DG', 'Note crédit client'),
('DO', 'Lettrage client'),
('DR', 'Es. clts+Fac. ces.im'),
('DV', 'Intérêts client'),
('DW', 'Paiement par effet'),
('DX', 'Rep. Clts Créd. TUP'),
('DY', 'Rep. Clts Deb. TUP'),
('DZ', 'Paiement Reçu'),
('EC', 'SyGS, ServtSS, TUP'),
('ER', 'Non utilisé'),
('EU', 'Non utilisé'),
('EX', 'Non utilisé'),
('FA', 'FACTURA ACRRED.IECI'),
('FB', 'F.CANJE SIMPLIFICADA'),
('FK', 'Factures Kyriba'),
('FP', 'Fact.Electron.Provee'),
('FS', 'FACTURAS SIMPLIFICAD'),
('FX', 'Foreign Exch. Kyriba'),
('IA', 'IFRS 15 extournable'),
('IB', 'IFRS15 Noextournable'),
('IE', 'KShuttle OD extourn'),
('IK', 'Intérêts Kyriba'),
('IN', 'KShuttle OD Non Exto'),
('IT', 'SyGS, ServtSS, TUP'),
('KA', 'Acompte Fournisseur'),
('KG', 'Avoir four. hors MM'),
('KN', 'Non utilisé'),
('KO', 'Lettrage fournisseur'),
('KP', 'Gestion EM/EF'),
('KR', 'Factures hors MM'),
('KX', 'Rep. Four. Cred. TUP'),
('KY', 'Rep. Four. Deb. TUP'),
('KZ', 'Paiement manuel émis'),
('M1', 'Non utilisé'),
('M2', 'Non utilisé'),
('M3', 'Non utilisé'),
('M4', 'Non utilisé'),
('M5', 'Non utilisé'),
('M6', 'Non utilisé'),
('M7', 'Non utilisé'),
('M8', 'Non utilisé'),
('M9', 'Non utilisé'),
('MD', 'Non utilisé'),
('MI', 'Trasl. o Pago de IVA'),
('MK', 'Ecrit Markup Blended'),
('ML', 'Non utilisé'),
('MX', 'SyGS, ServtSS, TUP'),
('PA', 'Ecriture de paye AS'),
('PC', 'PAGO FACT.PROV. IECI'),
('PE', 'SyGS, ServtSS, TUP'),
('PI', 'Écriture Pr. Immo.'),
('PN', 'SyGS, ServtSS, TUP'),
('PR', 'Modification du prix'),
('PT', 'SyGS, ServtSS, TUP'),
('R2', 'Factura rectificativ'),
('R4', 'RECTIFICATIVA R4'),
('RA', 'Non utilisé'),
('RB', 'Client Douteux Litig'),
('RD', 'SyGS, ServtSS, TUP'),
('RE', 'Fact. Four. MM 401-8'),
('RF', 'FACTURA INTERNA  EDI'),
('RK', 'Non utilisé'),
('RN', 'Non utilisé'),
('RV', 'Factures & avoirs SD'),
('SA', 'Journal d''OD'),
('SB', 'Solde TVA col/ded'),
('SC', 'Affectation Résultat'),
('SE', 'Non utilisé'),
('SF', 'CONTABI. SIFTEL'),
('SK', 'Pai. Enc. Cpta géné.'),
('SU', 'Lettrage GL'),
('TN', 'Non utilisé'),
('TV', 'Ecritures de TVA'),
('UE', 'Non utilisé'),
('WA', 'Sortie marchandises'),
('WE', 'FNP de MM Cpt 6-408'),
('WI', 'Pièce d''inventaire'),
('WL', 'Sortie march./livr.'),
('WN', 'Non utilisé'),
('WP', 'Non utilisé'),
('WR', 'Non utilisé'),
('WS', 'Non utilisé'),
('Y1', 'Non utilisé'),
('YA', 'Rec. Rev (FAE/PCA)PS'),
('YB', 'Ext.Rec.Re FAE/PCA'),
('Z1', 'SyGS, ServtSS, TUP'),
('Z2', 'SyGS, ServtSS, TUP'),
('Z3', 'Repr PCA chargnt'),
('Z4', 'Rep PCA com Pdt réel'),
('ZA', 'Prov PO + CP AS'),
('ZB', 'Rectificativaszmir12'),
('ZC', 'Nota de Cargo Iecisa'),
('ZD', 'Nota de Cargo Iecisa'),
('ZE', 'Non utilisé'),
('ZF', 'Anul-rev de Facturas'),
('ZK', 'Flux Tréso Kyriba'),
('ZP', 'Fnr+NDF+ Prelvt Clts'),
('ZR', 'Rel. Banc. 511/512'),
('ZS', 'Paiement par chèque'),
('ZU', 'Concur'),
('ZV', 'Lettrage au paiement'),
('ZY', 'Non utilisé'),
('ZZ', 'Non utilisé');

-------------

--SELECT [CodeTypeDoc]
--      ,[Description]
--  FROM [RAPPORT_DI_DEV].[dbo].[Info_ACDOCA]


SELECT * 
FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A.BELNR, 
        A.FISCYEARPER, 
        U.BUDAT, 
        A.BLDAT, 
        A.HSL, 
        A.OBJNR, 
        A.PERNR, 
        A.RUNIT, 
        A.RVUNIT, 
        A.VMSL, 
        A.ULSTAR, 
        U.ISDD, 
        A.RFAREA 
     FROM SAPHANADB.ACDOCA A 
     INNER JOIN SAPHANADB.AFRU U 
         ON U.RUECK = A.AWREF 
         AND U.RMZHL = A.AWORG 
     WHERE 
         (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'') 
         AND A.RLDNR = ''0L'' 
         AND A.ACCASTY = ''NV''
    ')

    select SETNAME from SETLEAF where SETCLASS=0102 and SETLEAF_VALFROM =”00” + ACDOCA_RACCT

    select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1=ACDOCA_RACCT


------------------

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A.BELNR, 
        A.FISCYEARPER, 
        U.BUDAT, 
        A.BLDAT, 
        A.HSL, 
        A.OBJNR, 
        A.PERNR, 
        A.RUNIT, 
        A.RVUNIT, 
        A.VMSL, 
        A.ULSTAR, 
        U.ISDD, 
        A.RFAREA 
     FROM SAPHANADB.ACDOCA A 
     INNER JOIN SAPHANADB.AFRU U 
         ON U.RUECK = A.AWREF 
         AND U.RMZHL = A.AWORG 
     WHERE 
         (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'') 
         AND A.RLDNR = ''0L'' 
         AND A.ACCASTY = ''NV''
    ')

    select SETNAME from SETLEAF where SETCLASS=0102 and SETLEAF_VALFROM = ”00” + ACDOCA_RACCT

    select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1=ACDOCA_RACCT

select * from openquery (SAP_P100_PROD,'select * from SAPHANADB.ACDOCA')

-- Grandlivre&Pl
select * from openquery (SAP_P100_PROD,'select RACCT,SGTXT,BLART, RFAREA from SAPHANADB.ACDOCA')
 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.BELNR,
        A.FISCYEARPER,
        U.BUDAT,
        A.BLDAT,
        A.HSL,
        A.OBJNR,
        A.PERNR,
        RUNIT,
        RVUNIT,
        VMSL,
        A.ULSTAR,
        U.ISDD,
        A.RFAREA
    FROM 
        SAPHANADB.ACDOCA A
    INNER JOIN  
        SAPHANADB.AFRU U 
        ON U.RUECK = A.AWREF AND U.RMZHL = A.AWORG
    WHERE 
        (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'')
        AND A.RLDNR = ''0L'' 
        AND A.ACCASTY = ''NV''
');


-- Type écriture jrnal : ACDOCA_BLART
-- Nom type écrit.jrnal : Créer une table fictive dans la base de données contenant les données indiquées dans le fichier ci-joint
-- Txte poste écr.jrnal : ACDOCA_SGTXT
-- Domaine fonctionnel : ACDOCA_RFAREA


-- Created by Copilot in SSMS - review carefully before executing
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT, 
        A.SGTXT, 
        A.BLART, 
        A.RFAREA, 
        S.SETNAME, 
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON A.RACCT = S.SETNAME
        AND S.SETCLASS = 0102 
        AND S.VALFROM = ''00''
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
    WHERE LEFT(A.BUDAT, 6) = LEFT(REPLACE(''202501'', ''-'', ''''), 6)
');
--------------
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT AS Compte_General, 
        A.SGTXT AS Texte_Ecriture_Journal, 
        A.BLART AS Type_Ecriture_Journal, 
        A.RFAREA AS Domaine_Fonctionnel, 
        S.SETNAME AS Info_Noeud_Hierarchique, 
        K.MCOD1 AS Designation_Compte_General
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON S.SETCLASS = 0102 
        AND S.VALFROM = CONCAT(''00'', A.RACCT)
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
');