SELECT * 
FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A<PERSON>NR, 
        A.FISCYEARPER, 
        U.BUDAT, 
        A.BLDAT, 
        A.H<PERSON>, 
        A.O<PERSON>, 
        A.<PERSON>, 
        A.RUNIT, 
        A.RVUNIT, 
        A.VMSL, 
        A.ULSTAR, 
        U.ISDD, 
        A.RFAREA 
     FROM SAPHANADB.ACDOCA A 
     INNER JOIN SAPHANADB.AFRU U 
         ON U.RUECK = A.AWREF 
         AND U.RMZHL = A.AWORG 
     WHERE 
         (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'') 
         AND A.RLDNR = ''0L'' 
         AND A.ACCASTY = ''NV''
    ')

------
select * from openquery (SAP_P100_PROD,'select * from SAPHANADB.ACDOCA')

-- Grandlivre&Pl
select * from openquery (SAP_P100_PROD,'select RACCT,SGTXT,BLART, RFAREA from SAPHANADB.ACDOCA')
 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A<PERSON>BELNR,
        A.FISCYEARPER,
        U.BUDAT,
        A.BLDAT,
        A.HSL,
        A.OBJNR,
        A.PERNR,
        RUNIT,
        RVUNIT,
        VMSL,
        A.ULSTAR,
        U.ISDD,
        A.RFAREA
    FROM SAPHANADB.ACDOCA A
    INNER JOIN  SAPHANADB.AFRU U ON U.RUECK = A.AWREF AND U.RMZHL = A.AWORG
    WHERE 
        (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'')
        AND A.RLDNR = ''0L'' 
        AND A.ACCASTY = ''NV''
');



-- Type écriture jrnal : ACDOCA_BLART
-- Nom type écrit.jrnal : Créer une table fictive dans la base de données contenant les données indiquées dans le fichier ci-joint
-- Txte poste écr.jrnal : ACDOCA_SGTXT
-- Domaine fonctionnel : ACDOCA_RFAREA


SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT RACCT, SGTXT, BLART, RFAREA 
     FROM SAPHANADB.ACDOCA 
     WHERE LEFT(BUDAT, 6) = LEFT(REPLACE(''202501'', ''-'', ''''), 6)');



-- Created 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT SETNAME
    FROM SAPHANADB.SETLEAF
    where SETCLASS = 0102 and VALFROM = ''00''
');

---
-- Created 

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND SKAT_MCOD1 = ACDOCA_RACCT
');
    --select SETNAME from SETLEAF where SETCLASS=0102 and SETLEAF_VALFROM=”00”+ACDOCA_RACCT
    --select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1 = ACDOCA_RACCT

-- Created  in SSMS - 

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND MCOD1 = RACCT
');


----
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT RACCT, SGTXT, BLART, RFAREA 
     FROM SAPHANADB.ACDOCA 
     WHERE LEFT(BUDAT, 6) = LEFT(REPLACE(''202501'', ''-'', ''''), 6)');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT SETNAME
    FROM SAPHANADB.SETLEAF
    where SETCLASS = 0102 and VALFROM = ''00''
');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND MCOD1 = RACCT
');


-- Created  in SSMS - 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT, 
        A.SGTXT, 
        A.BLART, 
        A.RFAREA, 
        S.SETNAME, 
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON A.RACCT = S.SETNAME
        AND S.SETCLASS = 0102 
        AND S.VALFROM = ''00''
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
');

INSERT INTO dbo.info_ACDOCA
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT AS Compte_General, 
        A.SGTXT AS Texte_Ecriture_Journal, 
        A.BLART AS Type_Ecriture_Journal, 
        A.RFAREA AS Domaine_Fonctionnel, 
        S.SETNAME AS Info_Noeud_Hierarchique, 
        K.MCOD1 AS Designation_Compte_General
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON S.SETCLASS = 0102 
        AND S.VALFROM = CONCAT(''00'', A.RACCT)
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
');

------
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
       *
    FROM SAPHANADB.SETLEAF S
');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
       *
    FROM SAPHANADB.SKAT K
');

---correction
-- Created by Copilot in SSMS - review carefully before executing
SELECT 
    S.SETNAME, 
    K.MCOD1, 
    A.RACCT, 
    A.SGTXT, 
    A.BLART, 
    A.RFAREA
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        SETNAME, 
        MANDT 
    FROM SAPHANADB.SETLEAF
') S
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        MCOD1, 
        MANDT 
    FROM SAPHANADB.SKAT
') K ON S.MANDT = K.MANDT
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        RACCT, 
        SGTXT, 
        BLART, 
        RFAREA, 
        RCLNT 
    FROM SAPHANADB.ACDOCA
') A ON S.MANDT = A.RCLNT;