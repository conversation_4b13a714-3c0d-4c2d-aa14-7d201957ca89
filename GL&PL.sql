SELECT * 
FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A<PERSON>NR, 
        A.FISCYEARPER, 
        U.BUDAT, 
        A.BLDAT, 
        A.H<PERSON>, 
        A.O<PERSON>, 
        A.<PERSON>, 
        A.RUNIT, 
        A.RVUNIT, 
        A.VMSL, 
        A.ULSTAR, 
        U.ISDD, 
        A.RFAREA 
     FROM SAPHANADB.ACDOCA A 
     INNER JOIN SAPHANADB.AFRU U 
         ON U.RUECK = A.AWREF 
         AND U.RMZHL = A.AWORG 
     WHERE 
         (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'') 
         AND A.RLDNR = ''0L'' 
         AND A.ACCASTY = ''NV''
    ')

------
select * from openquery (SAP_P100_PROD,'select * from SAPHANADB.ACDOCA')

-- Grandlivre&Pl
select * from openquery (SAP_P100_PROD,'select RACCT,SGTXT,BLART, RFAREA from SAPHANADB.ACDOCA')
 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A<PERSON>BELNR,
        A.FISCYEARPER,
        U.BUDAT,
        A.BLDAT,
        A.HSL,
        A.OBJNR,
        A.PERNR,
        RUNIT,
        RVUNIT,
        VMSL,
        A.ULSTAR,
        U.ISDD,
        A.RFAREA
    FROM SAPHANADB.ACDOCA A
    INNER JOIN  SAPHANADB.AFRU U ON U.RUECK = A.AWREF AND U.RMZHL = A.AWORG
    WHERE 
        (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'')
        AND A.RLDNR = ''0L'' 
        AND A.ACCASTY = ''NV''
');



-- Type écriture jrnal : ACDOCA_BLART
-- Nom type écrit.jrnal : Créer une table fictive dans la base de données contenant les données indiquées dans le fichier ci-joint
-- Txte poste écr.jrnal : ACDOCA_SGTXT
-- Domaine fonctionnel : ACDOCA_RFAREA


SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT RACCT, SGTXT, BLART, RFAREA 
     FROM SAPHANADB.ACDOCA 
     WHERE LEFT(BUDAT, 6) = LEFT(REPLACE(''202501'', ''-'', ''''), 6)');



-- Created 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT SETNAME
    FROM SAPHANADB.SETLEAF
    where SETCLASS = 0102 and VALFROM = ''00''
');

---
-- Created 

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND SKAT_MCOD1 = ACDOCA_RACCT
');
    --select SETNAME from SETLEAF where SETCLASS=0102 and SETLEAF_VALFROM=”00”+ACDOCA_RACCT
    --select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1 = ACDOCA_RACCT

-- Created  in SSMS - 

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND MCOD1 = RACCT
');


----
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT RACCT, SGTXT, BLART, RFAREA 
     FROM SAPHANADB.ACDOCA 
     WHERE LEFT(BUDAT, 6) = LEFT(REPLACE(''202501'', ''-'', ''''), 6)');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT SETNAME
    FROM SAPHANADB.SETLEAF
    where SETCLASS = 0102 and VALFROM = ''00''
');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT MCOD1
    FROM SAPHANADB.SKAT
    WHERE SPRAS = ''0102'' AND KTOPL = ''ZGFI'' AND MCOD1 = RACCT
');

 select MCOD1 from SKAT where SPRAS=0102 and KTOPL=ZGFI AND SKAT_MCOD1=ACDOCA_RACCT

-- Created  in SSMS - 
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT, 
        A.SGTXT, 
        A.BLART, 
        A.RFAREA, 
        S.SETNAME, 
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON A.RACCT = S.SETNAME
        AND S.SETCLASS = 0102 
        AND S.VALFROM = ''00''
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
');

INSERT INTO dbo.info_ACDOCA
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RACCT AS Compte_General, 
        A.SGTXT AS Texte_Ecriture_Journal, 
        A.BLART AS Type_Ecriture_Journal, 
        A.RFAREA AS Domaine_Fonctionnel, 
        S.SETNAME AS Info_Noeud_Hierarchique, 
        K.MCOD1 AS Designation_Compte_General
    FROM SAPHANADB.ACDOCA A
    INNER JOIN SAPHANADB.SETLEAF S
        ON S.SETCLASS = 0102 
        AND S.VALFROM = CONCAT(''00'', A.RACCT)
    INNER JOIN SAPHANADB.SKAT K
        ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
');

------
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
       *
    FROM SAPHANADB.SETLEAF S
');

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
       *
    FROM SAPHANADB.SKAT K
');

---correction
-- Created by Copilot in SSMS - review carefully before executing
SELECT 
    S.SETNAME, 
    K.MCOD1, 
    A.RACCT, 
    A.SGTXT, 
    A.BLART, 
    A.RFAREA
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        SETNAME, 
        MANDT 
    FROM SAPHANADB.SETLEAF
') S
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        MCOD1, 
        MANDT 
    FROM SAPHANADB.SKAT
') K ON S.MANDT = K.MANDT
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        RACCT, 
        SGTXT, 
        BLART, 
        RFAREA, 
        RCLNT 
    FROM SAPHANADB.ACDOCA
') A ON S.MANDT = A.RCLNT;

-----
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
    A.RACCT AS Compte_General, 
    A.SGTXT AS Texte_Ecriture_Journal, 
    A.BLART AS Type_Ecriture_Journal, 
    A.RFAREA AS Domaine_Fonctionnel
FROM SAPHANADB.ACDOCA A
INNER JOIN SAPHANADB.SKAT K ON K.MANDT = A.RCLNT
WHERE A.BLART BETWEEN ''1000'' AND ''2000''
');

  --K.MCOD1 AS Designation_Compte_General
  --S.SETNAME AS Info_Noeud_Hierarchique
  -- INNER JOIN SAPHANADB.SKAT K ON K.MANDT = A.RCLNT
  --INNER JOIN SAPHANADB.SETLEAF S ON S.MANDT = A.RCLNT

  SELECT 
    A.RACCT AS Compte_General, 
    A.SGTXT AS Texte_Ecriture_Journal, 
    A.BLART AS Type_Ecriture_Journal, 
    A.RFAREA AS Domaine_Fonctionnel
FROM SAPHANADB.ACDOCA A
INNER JOIN SAPHANADB.SKAT K ON K.MANDT = A.RCLNT
WHERE A.RFAREA IS NOT NULL -- Exemple de filtre
LIMIT 1000; -- Limiter le nombre de lignes

----
SELECT 
    A.RACCT AS Compte_General, 
    A.SGTXT AS Texte_Ecriture_Journal, 
    A.BLART AS Type_Ecriture_Journal, 
    A.RFAREA AS Domaine_Fonctionnel
FROM SAPHANADB.ACDOCA A
INNER JOIN SAPHANADB.SKAT K ON K.MANDT = A.RCLNT
WHERE A.BLART BETWEEN '1000' AND '2000';

-------

-- correction requête version finale
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT, A.SGTXT, A.BLART, A.RFAREA, K.MCOD1
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
     WHERE K.SPRAS = ''0102'' 
       AND K.KTOPL = ''ZGFI''');

--
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT,  A.BLART, A.RFAREA,S.SETNAME 
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
     WHERE S.SETCLASS = ''0102''');

-- Finale
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT S.SETNAME, K.MCOD1, A.RACCT, A.BLART, A.SGTXT, A.RFAREA, A.FISCYEARPER
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
     INNER JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
     WHERE K.SPRAS = ''0102'' 
       AND K.KTOPL = ''ZGFI''
       AND S.SETCLASS = ''0102''');

--------------
SELECT TOP (1000) [File]
      ,[Date_chargement]
      ,[type_file]
FROM [RAPPORT_DI].[dbo].[suivi_chargements]
WHERE [Date_chargement] >= '2025-06-01' 
  AND [Date_chargement] < '2025-07-01'
ORDER BY [Date_chargement] DESC; 
--------------

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.BELNR,
        A.FISCYEARPER,
        A.BLART,
        A.SGTXT,
        A.RACCT,
        U.BUDAT,
        A.BLDAT,
        A.HSL,
        A.OBJNR,
        A.PERNR,
        RUNIT,
        RVUNIT,
        VMSL,
        A.ULSTAR,
        U.ISDD,
        A.RFAREA
    FROM SAPHANADB.ACDOCA A
    INNER JOIN  SAPHANADB.AFRU U ON U.RUECK = A.AWREF AND U.RMZHL = A.AWORG
    WHERE 
        (LEFT(U.ISDD, 4) = ''2025'' OR LEFT(A.BUDAT, 4) = ''2025'')
        AND A.RLDNR = ''0L'' 
        AND A.ACCASTY = ''NV''
');

----

select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.ACDOCA')
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SKAT')
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SETLEAF')

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '                                    
    SELECT SETNAME 
    FROM SAPHANADB.SETLEAF 
    WHERE SETCLASS = ''0102'' 
      AND VALFROM = ''00''
');



--
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT, A.SGTXT, A.BLART, A.RFAREA, K.MCOD1
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
     WHERE K.SPRAS = ''0102'' 
       AND K.KTOPL = ''ZGFI''');

--
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT,  A.BLART, A.RFAREA,S.SETNAME 
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
     WHERE S.SETCLASS = ''0102''');

-- Finale
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT S.SETNAME, K.MCOD1, A.RACCT, A.BLART, A.SGTXT, A.RFAREA, A.FISCYEARPER
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
     INNER JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
     WHERE K.SPRAS = ''0102'' 
       AND K.KTOPL = ''ZGFI''
       AND S.SETCLASS = ''0102''');

SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT K.MCOD1
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
     WHERE K.SPRAS = ''0102'' 
       AND K.KTOPL = ''ZGFI''');

SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT S.SETNAME 
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
     WHERE S.SETCLASS = ''0102''');

----------------------------------------------------------------------------
SELECT 
    S.SETNAME, 
    K.MCOD1, 
    A.RACCT, 
    A.SGTXT, 
    A.BLART, 
    A.RFAREA
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        SETNAME, 
        MANDT 
    FROM SAPHANADB.SETLEAF
') S
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        MCOD1, 
        MANDT 
    FROM SAPHANADB.SKAT
') K ON S.MANDT = K.MANDT
INNER JOIN OPENQUERY(SAP_P100_PROD, '
    SELECT 
        RACCT, 
        SGTXT, 
        BLART, 
        RFAREA, 
        RCLNT 
    FROM SAPHANADB.ACDOCA
') A ON S.MANDT = A.RCLNT;

-- creation de la table GLPL en base de données 

CREATE TABLE GLPL (
    RACCT  VARCHAR(50),
    SGTXT  VARCHAR(255),
    BLART  VARCHAR(10),
    RFAREA VARCHAR(10),
    BUDAT  VARCHAR(10), -- ou BIGINT ou VARCHAR(8) selon le type de idtemps dans dimtemps
);

-- Numéro de compte : RACCT
-- Domaine fonctionnel : RFAREA
-- Date comptable : BUDA, est-ce que la date comptable est prise en compte ?



RLDNR : Ledger

RBUKRS : Société

BELNR : numéro de document

RACCT : nature comptable

SCNTR : centre de coût / collaborateur

PRCTR : centre de profit récepteur (du projet)

PPRCTR : centre de profit émetteur (du collaborateur)

HSL : montant

RHCUR : devise

MSL : quantité

RUNIT : unité de la quantité

FISCYEARPER : période/année

LSTAR : type d’activité

ACCAS : activité/réseau

OBJNR : l’élément d’imputation (CC/WBS)

----- A voir avec Maeva demain 

SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR,
        A.RBUKRS,
        A.BELNR,
        A.RACCT,
        A.SCNTR,
        A.PRCTR,
        A.PPRCTR, 
        A.HSL,
        A.RHCUR,
        A.MSL, 
        A.RUNIT, 
        A.FISCYEARPER, 
        A.LSTAR, 
        A.ACCAS, 
        A.OBJNR,  
        A.SGTXT, 
        A.BLART, 
        A.RFAREA, 
        S.SETNAME, 
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    LEFT OUTER JOIN SAPHANADB.SETLEAF S ON A.RACCT = S.SETNAME
    LEFT OUTER JOIN SAPHANADB.SKAT K ON K.MCOD1 = CONCAT(''00'', A.RACCT)
    WHERE 
        (LEFT(A.BUDAT, 4) = ''2025'')
        
');

-----dernière version

----
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR AS "Ledger",
        A.RBUKRS AS "Société",
        A.BELNR AS "Numéro de document",
        A.RACCT AS "Nature comptable",
        A.SCNTR AS "Centre de coût / collaborateur",
        A.PRCTR AS "Centre de profit récepteur (du projet)",
        A.PPRCTR AS "Centre de profit émetteur (du collaborateur)", 
        A.HSL,
        A.RHCUR AS "Devise",
        A.MSL AS "Quantité", 
        A.RUNIT AS "Unité de la quantité", 
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d’activité", 
        A.ACCAS AS "Activité/Réseau", 
        A.OBJNR AS " Élément d’imputation (CC/WBS)",  
        A.SGTXT AS "Texte poste écr.jrnal", 
        A.BLART AS "Type écriture jrnal", 
        A.RFAREA AS "Domaine fonctionnel", 
        S.VALFROM || A.RACCT AS "Info nœud hiérarchique",
        K.MCOD1 AS "DésLgue cpte général"
    FROM SAPHANADB.ACDOCA A
    LEFT JOIN SAPHANADB.SETLEAF S ON A.RACCT = S.SETNAME 
        AND S.SETCLASS = 0102 
        AND S.VALFROM LIKE ''00%''
    LEFT JOIN SAPHANADB.SKAT K ON K.MCOD1 =  A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
    WHERE 
        LEFT(A.BUDAT, 4) = ''2025''   
');

-------Test de requête 

USE RAPPORT_DI
GO

-------
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR AS "Ledger",
        A.RBUKRS AS "Société",
        A.BELNR AS "Numéro de document",
        A.RACCT AS "Nature comptable",
        A.SCNTR AS "Centre de coût / collaborateur",
        A.PRCTR AS "Centre de profit récepteur (du projet)",
        A.PPRCTR AS "Centre de profit émetteur (du collaborateur)", 
        A.HSL,
        A.RHCUR AS "Devise",
        A.MSL AS "Quantité", 
        A.RUNIT AS "Unité de la quantité", 
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d’activité", 
        A.ACCAS AS "Activité/Réseau", 
        A.OBJNR AS " Élément d’imputation (CC/WBS)",  
        A.SGTXT AS "Texte poste écr.jrnal", 
        A.BLART AS "Type écriture jrnal", 
        A.RFAREA AS "Domaine fonctionnel", 
        S.VALFROM || A.RACCT AS "Info nœud hiérarchique",
        K.MCOD1 AS "DésLgue cpte général"
    FROM SAPHANADB.ACDOCA A
   LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
    AND S.SETCLASS = ''0102''
    LEFT JOIN SAPHANADB.SKAT K ON K.SAKNR =  A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
    WHERE 
        LEFT(A.BUDAT, 4) = ''2025''   
');
-----------------------------
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SKAT')
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SETLEAF')

----
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT, A.SGTXT, A.BLART, A.RFAREA, K.MCOD1, S.SETNAME as INFO_NOEUD_HIERARCHIQUE
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
         AND K.SPRAS = ''R''
         AND K.KTOPL = ''CACO''
         AND K.MANDT = ''100''
     LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = A.RACCT
         AND S.SETCLASS = ''0101''
         AND S.MANDT = ''100''
     WHERE A.RCLNT = ''100''
       AND A.GJAHR = ''2025''');

----
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR AS "Ledger",
        A.RBUKRS AS "Société",
        A.BELNR AS "Numéro de document",
        A.RACCT AS "Nature comptable",
        A.SCNTR AS "Centre de coût / collaborateur",
        A.PRCTR AS "Centre de profit récepteur (du projet)",
        A.PPRCTR AS "Centre de profit émetteur (du collaborateur)", 
        A.HSL,
        A.RHCUR AS "Devise",
        A.MSL AS "Quantité", 
        A.RUNIT AS "Unité de la quantité", 
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d’activité", 
        A.ACCAS AS "Activité/Réseau", 
        A.OBJNR AS " Élément d’imputation (CC/WBS)",  
        A.SGTXT AS "Texte poste écr.jrnal", 
        A.BLART AS "Type écriture jrnal", 
        A.RFAREA AS "Domaine fonctionnel"
    FROM SAPHANADB.ACDOCA A');

------
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A.RBUKRS AS "Société",
        A.RACCT AS "Nature comptable",
        A.HSL,
        A.RHCUR AS "Devise",
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d''activité",
        A.SGTXT AS "Texte poste écr.jrnal",
        K.MCOD1 AS "Description compte",
        S.SETNAME AS "Info nœud hiérarchique"
     FROM SAPHANADB.ACDOCA A
     LEFT JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
         AND K.SPRAS = ''R''
         AND K.KTOPL = ''CACO''
         AND K.MANDT = ''100''
     LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = A.RACCT
         AND S.SETCLASS = ''0101''
         AND S.MANDT = ''100''
         AND S.VALSIGN = ''I''
     WHERE A.RBUKRS = ''1100''
       AND A.FISCYEARPER >= ''2025001''
       AND A.FISCYEARPER <= ''2025012''
     ORDER BY A.FISCYEARPER, A.RACCT
     LIMIT 1000');
---------
SELECT 
    A.RBUKRS AS "Société",
    A.RACCT AS "Nature comptable",
    A.HSL AS "Montant",
    A.RHCUR AS "Devise",
    A.FISCYEARPER AS "Période/Année",
    A.BUDAT AS "Date comptabilisation",
    A.LSTAR AS "Type d'activité",
    -- Info nœud hiérarchique
    S.SETNAME AS "Info nœud hiérarchique",
    -- Désignation longue compte général
    K.MCOD1 AS "DésLgue cpte général",
    -- Type écriture journal
    A.BLART AS "Type écriture jrnal",
    -- Nom type écriture journal (table fictive)
    T.NOM_TYPE AS "Nom type écrit.jrnal",
    -- Texte poste écriture journal
    A.SGTXT AS "Txte poste écr.jrnal",
    -- Domaine fonctionnel
    A.RFAREA AS "Domaine fonctionnel"
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RBUKRS,
        A.RACCT,
        A.HSL,
        A.RHCUR,
        A.FISCYEARPER,
        A.BUDAT,
        A.LSTAR,
        A.BLART,
        A.SGTXT,
        A.RFAREA,
        S.SETNAME,
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
        AND S.SETCLASS = ''0102''
    LEFT JOIN SAPHANADB.SKAT K ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102''
        AND K.KTOPL = ''ZGFI''
    WHERE A.RBUKRS = ''1100''
      AND A.FISCYEARPER >= ''2024001''
      AND A.FISCYEARPER <= ''2024012''
') A
-- Jointure avec la table fictive des types d'écriture
LEFT JOIN [RAPPORT_DI_DEV].[dbo].[Info_ACDOCA] T ON T.CodeTypeDoc = A.BLART;

----
USE RAPPORT_DI
GO

-------
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR AS "Ledger",
        A.RBUKRS AS "Société",
        A.BELNR AS "Numéro de document",
        A.RACCT AS "Nature comptable",
        A.SCNTR AS "Centre de coût / collaborateur",
        A.PRCTR AS "Centre de profit récepteur (du projet)",
        A.PPRCTR AS "Centre de profit émetteur (du collaborateur)", 
        A.HSL,
        A.RHCUR AS "Devise",
        A.MSL AS "Quantité", 
        A.RUNIT AS "Unité de la quantité", 
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d’activité", 
        A.ACCAS AS "Activité/Réseau", 
        A.OBJNR AS " Élément d’imputation (CC/WBS)",  
        A.SGTXT AS "Texte poste écr.jrnal", 
        A.BLART AS "Type écriture jrnal", 
        A.RFAREA AS "Domaine fonctionnel", 
        S.VALFROM || A.RACCT AS "Info nœud hiérarchique",
        K.MCOD1 AS "DésLgue cpte général"
    FROM SAPHANADB.ACDOCA A
   LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
    AND S.SETCLASS = ''0102''
    LEFT JOIN SAPHANADB.SKAT K ON K.SAKNR =  A.RACCT
        AND K.SPRAS = ''0102'' 
        AND K.KTOPL = ''ZGFI''
    WHERE 
        LEFT(A.BUDAT, 4) = ''2025''   
');
-----------------------------
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SKAT')
select *  from openquery (SAP_P100_PROD, 'SELECT * FROM SAPHANADB.SETLEAF')

----
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT A.RACCT, A.SGTXT, A.BLART, A.RFAREA, K.MCOD1, S.SETNAME as INFO_NOEUD_HIERARCHIQUE
     FROM SAPHANADB.ACDOCA A
     INNER JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
         AND K.SPRAS = ''R''
         AND K.KTOPL = ''CACO''
         AND K.MANDT = ''100''
     LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = A.RACCT
         AND S.SETCLASS = ''0101''
         AND S.MANDT = ''100''
     WHERE A.RCLNT = ''100''
       AND A.GJAHR = ''2025''');

----
SELECT * 
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RLDNR AS "Ledger",
        A.RBUKRS AS "Société",
        A.BELNR AS "Numéro de document",
        A.RACCT AS "Nature comptable",
        A.SCNTR AS "Centre de coût / collaborateur",
        A.PRCTR AS "Centre de profit récepteur (du projet)",
        A.PPRCTR AS "Centre de profit émetteur (du collaborateur)", 
        A.HSL,
        A.RHCUR AS "Devise",
        A.MSL AS "Quantité", 
        A.RUNIT AS "Unité de la quantité", 
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d’activité", 
        A.ACCAS AS "Activité/Réseau", 
        A.OBJNR AS " Élément d’imputation (CC/WBS)",  
        A.SGTXT AS "Texte poste écr.jrnal", 
        A.BLART AS "Type écriture jrnal", 
        A.RFAREA AS "Domaine fonctionnel"
    FROM SAPHANADB.ACDOCA A');

------
SELECT * FROM OPENQUERY(SAP_P100_PROD, 
    'SELECT 
        A.RBUKRS AS "Société",
        A.RACCT AS "Nature comptable",
        A.HSL,
        A.RHCUR AS "Devise",
        A.FISCYEARPER AS "Période/Année",
        A.BUDAT,
        A.LSTAR AS "Type d''activité",
        A.SGTXT AS "Texte poste écr.jrnal",
        K.MCOD1 AS "Description compte",
        S.SETNAME AS "Info nœud hiérarchique"
     FROM SAPHANADB.ACDOCA A
     LEFT JOIN SAPHANADB.SKAT K ON K.SAKNR = A.RACCT
         AND K.SPRAS = ''R''
         AND K.KTOPL = ''CACO''
         AND K.MANDT = ''100''
     LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = A.RACCT
         AND S.SETCLASS = ''0101''
         AND S.MANDT = ''100''
         AND S.VALSIGN = ''I''
     WHERE A.RBUKRS = ''1100''
       AND A.FISCYEARPER >= ''2025001''
       AND A.FISCYEARPER <= ''2025012''
     ORDER BY A.FISCYEARPER, A.RACCT
     LIMIT 1000');
---------
USE RAPPORT_DI_DEV
GO


SELECT 
    A.RBUKRS AS "Société",
    A.RACCT AS "Nature comptable",
    A.HSL AS "Montant",
    A.RHCUR AS "Devise",
    A.FISCYEARPER AS "Période/Année",
    A.BUDAT AS "Date comptabilisation",
    A.LSTAR AS "Type d'activité",
    -- Info nœud hiérarchique
    S.SETNAME AS "Info nœud hiérarchique",
    -- Désignation longue compte général
    K.MCOD1 AS "DésLgue cpte général",
    -- Type écriture journal
    A.BLART AS "Type écriture jrnal",
    -- Nom type écriture journal (table fictive)
    T.NOM_TYPE AS "Nom type écrit.jrnal",
    -- Texte poste écriture journal
    A.SGTXT AS "Txte poste écr.jrnal",
    -- Domaine fonctionnel
    A.RFAREA AS "Domaine fonctionnel"
FROM OPENQUERY(SAP_P100_PROD, '
    SELECT 
        A.RBUKRS,
        A.RACCT,
        A.HSL,
        A.RHCUR,
        A.FISCYEARPER,
        A.BUDAT,
        A.LSTAR,
        A.BLART,
        A.SGTXT,
        A.RFAREA,
        S.SETNAME,
        K.MCOD1
    FROM SAPHANADB.ACDOCA A
    LEFT JOIN SAPHANADB.SETLEAF S ON S.VALFROM = CONCAT(''00'', A.RACCT)
        AND S.SETCLASS = ''0102''
    LEFT JOIN SAPHANADB.SKAT K ON K.MCOD1 = A.RACCT
        AND K.SPRAS = ''0102''
        AND K.KTOPL = ''ZGFI''
    WHERE A.RBUKRS = ''1100''
      AND A.FISCYEARPER >= ''2024001''
      AND A.FISCYEARPER <= ''2024012''
') A
-- Jointure avec la table fictive des types d'écriture
LEFT JOIN [RAPPORT_DI_DEV].[dbo].[Info_ACDOCA] T ON T.CodeTypeDoc = A.BLART;